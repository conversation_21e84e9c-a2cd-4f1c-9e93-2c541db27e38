import { routing } from "@i18n/routing";
import { config as appConfig } from "@repo/config";
import { createPurchasesHelper } from "@repo/payments/lib/helper";
import {
	getOrganizationsForSession,
	getPurchasesForSession,
	getSession,
} from "@shared/lib/middleware-helpers";
import createMiddleware from "next-intl/middleware";
import { type NextRequest, NextResponse } from "next/server";
import { withQuery } from "ufo";

const intlMiddleware = createMiddleware(routing);

export default async function middleware(req: NextRequest) {
	const { pathname, origin } = req.nextUrl;

	if (pathname.startsWith("/app")) {
		const response = NextResponse.next();

		if (!appConfig.ui.saas.enabled) {
			return NextResponse.redirect(new URL("/", origin));
		}

		const session = await getSession(req);
		let locale = req.cookies.get(appConfig.i18n.localeCookieName)?.value;

		if (!session) {
			return NextResponse.redirect(
				new URL(
					withQuery("/auth/login", {
						redirectTo: pathname,
					}),
					origin,
				),
			);
		}

		if (
			appConfig.users.enableOnboarding &&
			!session.user.onboardingComplete &&
			pathname !== "/app/onboarding"
		) {
			return NextResponse.redirect(
				new URL(
					withQuery("/app/onboarding", {
						redirectTo: pathname,
					}),
					origin,
				),
			);
		}

		if (
			!locale ||
			(session.user.locale && locale !== session.user.locale)
		) {
			locale = session.user.locale ?? appConfig.i18n.defaultLocale;
			response.cookies.set(appConfig.i18n.localeCookieName, locale);
		}

		/* 原代码:
		 * if (
		 * 	appConfig.organizations.enable &&
		 * 	appConfig.organizations.requireOrganization &&
		 * 	pathname === "/app"
		 * ) {
		 * 	const organizations = await getOrganizationsForSession(req);
		 * 	const organization =
		 * 		organizations.find(
		 * 			(org) => org.id === session?.session.activeOrganizationId,
		 * 		) || organizations[0];
		 * 
		 * 	return NextResponse.redirect(
		 * 		new URL(
		 * 			organization
		 * 				? `/app/${organization.slug}`
		 * 				: "/app/new-organization",
		 * 			origin,
		 * 		),
		 * 	);
		 * }
		 * 修改原因: 当requireOrganization为false时，需要根据用户的活跃组织状态进行路由处理
		 * 修改时间: 2025-04-29
		 * 修改人: LLM
		 * 关联需求: 登录流程修改 - 有活跃组织时跳转到组织页面，没有时进入个人页面
		 * 恢复方法: 删除当前代码，取消上方原代码的注释
		 */
		if (
			appConfig.organizations.enable &&
			pathname === "/app"
		) {
			const organizations = await getOrganizationsForSession(req);
			
			if (appConfig.organizations.requireOrganization) {
				// 必须有组织的情况
				const organization = 
					organizations.find(
						(org) => org.id === session?.session.activeOrganizationId,
					) || organizations[0];
				
				/* 原代码:
				 * return NextResponse.redirect(
				 * 	new URL(
				 * 		organization
				 * 			? `/app/${organization.slug}`
				 * 			: "/app/new-organization",
				 * 		origin,
				 * 	),
				 * );
				 * 修改原因: 当用户无权创建组织且没有组织时，重定向到无组织提示页面
				 * 修改时间: 2025-06-28
				 * 修改人: LLM
				 * 关联需求: 方案三 - 创建专门的无组织用户页面
				 * 恢复方法: 删除当前代码，取消上方原代码的注释
				 */
				
				// 重定向逻辑：根据用户的组织状态和权限决定跳转目标
				return NextResponse.redirect(
					new URL(
						organization
							// 情况1：用户有组织 -> 跳转到组织主页
							? `/app/${organization.slug}`
							// 情况2：用户无组织 -> 根据创建权限决定跳转目标
							: appConfig.organizations.enableUsersToCreateOrganizations
								// 情况2a：有创建权限 -> 跳转到创建组织页面
								? "/app/new-organization"
								// 情况2b：无创建权限 -> 跳转到无组织提示页面（新增逻辑）
								: "/app/no-organization",
						origin,
					),
				);
			}
			
			// 有活跃组织时重定向到该组织
			if (session?.session.activeOrganizationId) {
				const activeOrganization = organizations.find(
					(org) => org.id === session.session.activeOrganizationId
				);
				
				if (activeOrganization) {
					return NextResponse.redirect(
						new URL(`/app/${activeOrganization.slug}`, origin),
					);
				}
			}
		}

		const hasFreePlan = Object.values(appConfig.payments.plans).some(
			(plan) => "isFree" in plan,
		);
		if (
			((appConfig.organizations.enable &&
				appConfig.organizations.enableBilling) ||
				appConfig.users.enableBilling) &&
			!hasFreePlan
		) {
			const organizationId = appConfig.organizations.enable
				? session?.session.activeOrganizationId ||
					(await getOrganizationsForSession(req))?.at(0)?.id
				: undefined;

			const purchases = await getPurchasesForSession(req, organizationId);
			const { activePlan } = createPurchasesHelper(purchases);

			const validPathsWithoutPlan = [
				"/app/choose-plan",
				"/app/onboarding",
				"/app/new-organization",
				"/app/organization-invitation/",
			];
			if (
				!activePlan &&
				!validPathsWithoutPlan.some((path) => pathname.startsWith(path))
			) {
				return NextResponse.redirect(
					new URL("/app/choose-plan", origin),
				);
			}
		}

		return response;
	}

	if (pathname.startsWith("/auth")) {
		if (!appConfig.ui.saas.enabled) {
			return NextResponse.redirect(new URL("/", origin));
		}

		const session = await getSession(req);

		if (session && pathname !== "/auth/reset-password") {
			return NextResponse.redirect(new URL("/app", origin));
		}

		return NextResponse.next();
	}

	if (!appConfig.ui.marketing.enabled) {
		return NextResponse.redirect(new URL("/app", origin));
	}

	return intlMiddleware(req);
}

export const config = {
	matcher: [
		"/((?!api|image-proxy|images|fonts|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt).*)",
	],
};
